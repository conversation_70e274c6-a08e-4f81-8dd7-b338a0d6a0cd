<template>
  <view class="container">
    <!-- 自定义 loading 状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">页面加载中...</text>
    </view>

    <!-- web-view 组件，加载完成后显示 -->
    <web-view
      :src="webviewUrl"
      @load="onWebviewLoad"
      @error="onWebviewError"
      :class="{ 'webview-hidden': isLoading }"
      @message="handleMessage"
      :webview-styles="webviewStyles"
    ></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoading: true,
      webviewUrl: 'http://*************:5173/',
      webviewStyles: {
        progress: {
          color: '#FF3333'
        }
      }
    }
  },
  methods: {
    // webview 加载完成事件
    onWebviewLoad() {
      console.log('webview 加载完成');
      this.isLoading = false;
    },

    handleMessage(e) {
      console.log(e)
    },

    // webview 加载错误事件
    onWebviewError(error) {
      console.error('webview 加载错误:', error);
      this.isLoading = false;
      // 可以在这里添加错误处理逻辑，比如显示错误提示
    }
  }
}
</script>

<style>
.container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.webview-hidden {
  opacity: 0;
  position: absolute;
  top: -9999px;
  left: -9999px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
