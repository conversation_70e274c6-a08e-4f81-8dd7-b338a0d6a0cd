import request from '@/utils/http'

/**
 * 聊天相关 API
 */

/**
 * 流式聊天 - 知识库对话
 * @param {Object} params 聊天参数
 * @param {string} params.query 用户问题
 * @param {string} params.knowledge_base_name 知识库名称
 * @param {Array} params.history 历史对话
 * @param {boolean} params.stream 是否流式输出
 * @param {Object} params.config 其他配置参数
 * @param {AbortController} params.abortController 可选的取消控制器
 */
export async function streamChatWithKnowledge(params) {
  const { query, knowledge_base_name, history = [], stream = true, abortController, ...config } = params

  const requestBody = {
    query,
    knowledge_base_name,
    stream,
    history,
    ...config
  }

  // 构建请求 URL
  const baseURL = config.streamBaseURL || '/stream-api'
  const url = `${baseURL}/chat/knowledge_base_chat`

  // 获取认证 token
  const token = uni.getStorageSync('token')

  // 使用 fetch 进行真正的流式请求
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody),
      signal: abortController?.signal // 支持请求取消
    })

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status} ${response.statusText}`)
    }

    console.log('流式请求成功:', response.status)
    return response
  } catch (error) {
    console.error('流式请求失败:', error)
    throw error
  }
}

/**
 * 普通聊天请求 - 知识库对话
 * @param {Object} data 聊天数据
 */
export function chatWithKnowledge(data) {
  return request({
    url: 'techDocManage/knowledge/answerResult',
    method: 'post',
    data: JSON.stringify(data),
    header: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取知识库聊天记录
 * @param {Object} params 查询参数
 */
export function getKnowledgeChatList(params) {
  return request({
    url: 'techDocManage/knowledge/getKnowledgeChatByKnowledgeName',
    method: 'get',
    params
  })
}

/**
 * 获取知识库聊天数量统计
 * @param {Object} params 查询参数
 */
export function getKnowledgeChatNum(params) {
  return request({
    url: 'techDocManage/knowledge/getKnowledgeChatNum',
    method: 'get',
    params
  })
}

/**
 * 添加聊天记录
 * @param {Object} data 聊天数据
 */
export function addChat(data) {
  return request({
    url: 'techDocManage/knowledge/addChat',
    method: 'post',
    data
  })
}

/**
 * 下载HTML内容为Word文档
 * @param {Object} data 下载参数
 * @param {string} data.htmlStr HTML字符串内容
 */
export function downloadDocByHtml(data) {
  return request({
    url: 'assisted/repRecord/downloadDocByHtml',
    method: 'post',
    data: data,
    responseType: 'blob',
    custom: {
      isBlob: true
    }
  })
}
