<script setup lang="ts">
import { ref, computed } from 'vue'
import { copyToClipboard } from '@/utils/clipboard.js'
import knowledgeApi from '@/api/knowledge'

import MarkdownIt from 'markdown-it'
import { useUserStore } from '@/store/user'
import config from '@/config'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content?: string
  answer?: string
  liked?: boolean
  historyId?: string
}

interface Props {
  message: Message
}

const props = defineProps<Props>()

const emit = defineEmits<{
  delete: [id: string]
  like: [data: { message: Message; liked: boolean }]
}>()

// 响应式状态
const isVoicePlaying = ref(false)
const isDownloading = ref(false)

// markdown-it 实例
const md = new MarkdownIt()

// 语音合成相关 - 在uni-app中使用原生API
let speechSynthesis: any = null
let speechUtterance: any = null

// 初始化语音合成
const initSpeech = () => {
  if (typeof window !== 'undefined' && window.speechSynthesis) {
    speechSynthesis = window.speechSynthesis
    speechUtterance = new SpeechSynthesisUtterance()
  }
}

// 提取纯文本内容
const extractPlainText = (text: string): string => {
  if (!text) return ''
  if (!text.includes('<')) {
    return text.trim()
  }
  return text.replace(/<[^>]*>/g, '').trim()
}

// 移除think标签的内容
const getContentWithoutThink = (text: string): string => {
  if (!text) return ''

  let content = text

  // 移除完整的think标签
  content = content.replace(/<think>[\s\S]*?<\/think>/g, '')

  // 移除未闭合的think标签
  content = content.replace(/<think>[\s\S]*$/, '')

  return content.trim()
}

// 复制功能
const handleCopy = async () => {
  const messageContent = props.message.content || props.message.answer || ''
  if (!messageContent) {
    uni.showToast({
      title: '没有可复制的内容',
      icon: 'none'
    })
    return
  }

  // 移除think标签后复制
  const contentToCopy = getContentWithoutThink(messageContent)
  if (!contentToCopy.trim()) {
    uni.showToast({
      title: '没有可复制的内容',
      icon: 'none'
    })
    return
  }

  try {
    await copyToClipboard(contentToCopy)
    uni.showToast({
      title: '复制成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('复制失败:', error)
    uni.showToast({
      title: '复制失败',
      icon: 'none'
    })
  }
}

// 删除功能
const handleDelete = () => {
  emit('delete', props.message.id)
}

// 语音播放功能
const handleVoicePlay = () => {
  if (!speechSynthesis || !speechUtterance) {
    initSpeech()
  }

  if (!speechSynthesis) {
    uni.showToast({
      title: '当前环境不支持语音播放',
      icon: 'none'
    })
    return
  }

  speechSynthesis.cancel()

  if (isVoicePlaying.value) {
    speechSynthesis.cancel()
    isVoicePlaying.value = false
  } else {
    const messageContent = props.message.content || props.message.answer || ''
    const plainText = extractPlainText(messageContent)
    if (!plainText) {
      uni.showToast({
        title: '没有可播放的文本内容',
        icon: 'none'
      })
      return
    }

    speechUtterance.text = plainText
    speechUtterance.lang = 'zh-CN'
    speechUtterance.volume = 1
    speechUtterance.rate = 1
    speechUtterance.pitch = 1

    speechUtterance.onend = () => {
      isVoicePlaying.value = false
    }

    speechUtterance.onerror = (event: any) => {
      console.error('语音播放错误:', event)
      isVoicePlaying.value = false

      if (event.error && event.error !== 'interrupted' && event.error !== 'canceled') {
        uni.showToast({
          title: '语音播放失败',
          icon: 'none'
        })
      }
    }

    speechSynthesis.speak(speechUtterance)
    isVoicePlaying.value = true
  }
}

// 点赞功能
const handleLike = async () => {
  try {
    // 检查是否有historyId，只有已保存的历史记录才能点赞
    if (!props.message.historyId) {
      uni.showToast({
        title: '只有已保存的历史记录才能点赞',
        icon: 'none'
      })
      return
    }

    const newLikedState = !props.message.liked

    // 调用点赞API - 使用PC端相同的参数格式
    const response = await knowledgeApi.giveThumbsUp({
      id: props.message.historyId,
      likeValue: newLikedState ? 1 : 0
    })

    if (response.code === 200) {
      emit('like', {
        message: props.message,
        liked: newLikedState
      })

      uni.showToast({
        title: newLikedState ? '点赞成功' : '取消点赞',
        icon: 'success'
      })
    } else {
      throw new Error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 将blob转换为base64的辅助函数
const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // 移除data:application/octet-stream;base64,前缀，只保留base64数据
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

// 下载功能
const handleDownload = async () => {
  if (isDownloading.value) return

  const messageContent = props.message.content || props.message.answer || ''
  if (!messageContent) {
    uni.showToast({
      title: '没有可下载的内容',
      icon: 'none'
    })
    return
  }

  // 移除think标签后获取内容
  const contentToDownload = getContentWithoutThink(messageContent)
  if (!contentToDownload.trim()) {
    uni.showToast({
      title: '没有可下载的内容',
      icon: 'none'
    })
    return
  }

  isDownloading.value = true

  try {
    // 使用markdown-it转为HTML
    const htmlStr = md.render(contentToDownload)

    // 使用原生fetch
    const userStore = useUserStore()
    const baseURL = config.baseURL || ''

    const response = await fetch(`${baseURL}assisted/repRecord/downloadDocByHtml`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userStore.token}`
      },
      body: JSON.stringify({ htmlStr })
    })

    if (!response.ok) {
      throw new Error('网络请求失败')
    }

    const cdHeader = response.headers.get('content-disposition') || ''
    const blob = await response.blob()

    let filename = ''
    if (cdHeader) {
      const matchStar = cdHeader.match(/filename\*=[^']*''([^;]+)/i)
      if (matchStar && matchStar[1]) {
        try {
          filename = decodeURIComponent(matchStar[1])
        } catch (_) {
          filename = matchStar[1]
        }
      } else {
        const match = cdHeader.match(/filename="?([^";]+)"?/i)
        if (match && match[1]) {
          filename = match[1]
        }
      }
    }

    if (!filename) {
      const ts = Date.now()
      filename = `download_${ts}.docx`
    }

    // 将blob转换为base64
    const base64Data = await blobToBase64(blob)

    // 通过postMessage发送下载请求给APP
    webUni.postMessage({
      data: {
        action: 'download',
        filename: filename,
        fileData: base64Data,
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      }
    })

    uni.showToast({
      title: '下载请求已发送',
      icon: 'success'
    })
  } catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    })
  } finally {
    isDownloading.value = false
  }
}

// 组件销毁时清理语音
const cleanup = () => {
  if (speechSynthesis) {
    speechSynthesis.cancel()
  }
}

// 初始化
initSpeech()

// 生命周期
import { onUnmounted } from 'vue'
onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <view class="message-toolbar">
    <!-- 复制按钮 -->
    <view class="toolbar-button" @click="handleCopy">
      <image class="button-icon" src="../icons/copy.svg" mode="aspectFit" />
      <text class="button-text">复制</text>
    </view>

    <!-- 删除按钮 -->
    <view class="toolbar-button" @click="handleDelete">
      <image class="button-icon" src="../icons/delete.svg" mode="aspectFit" />
      <text class="button-text">删除</text>
    </view>

    <!-- 语音播放按钮 -->
    <view
      class="toolbar-button"
      :class="{ active: isVoicePlaying }"
      @click="handleVoicePlay"
    >
      <image class="button-icon" src="../icons/voice-play.svg" mode="aspectFit" />
      <text class="button-text">{{ isVoicePlaying ? '停止播放' : '语音播放' }}</text>
    </view>

    <!-- 点赞按钮 -->
    <view
      class="toolbar-button"
      :class="{ active: message.liked }"
      @click="handleLike"
    >
      <image class="button-icon" src="../icons/like.svg" mode="aspectFit" />
      <text class="button-text">{{ message.liked ? '取消点赞' : '点赞' }}</text>
    </view>

    <!-- 下载按钮 -->
    <view
      class="toolbar-button"
      :class="{ disabled: isDownloading }"
      @click="handleDownload"
    >
      <image class="button-icon" src="../icons/download.svg" mode="aspectFit" />
      <text class="button-text">{{ isDownloading ? '下载中...' : '下载' }}</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.message-toolbar {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #E8E8E8;
  margin-top: 24rpx;
  flex-wrap: wrap;
}

.toolbar-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: transparent;
  transition: background-color 0.2s ease;
  cursor: pointer;
  min-height: 60rpx;

  &:active {
    background-color: #F5F6F7;
  }

  &.active {
    color: $accent-color;
    background-color: rgba(20, 91, 255, 0.1);

    .button-icon {
      color: $accent-color;
    }
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.button-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}

.button-text {
  font-size: 26rpx;
  color: $text-primary;
  white-space: nowrap;
}

.active .button-text {
  color: $accent-color;
}
</style>
