<template>
  <view class="container">
    <!-- 自定义 loading 状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">页面加载中...</text>
    </view>

    <!-- web-view 组件，加载完成后显示 -->
    <web-view
      :src="webviewUrl"
      @load="onWebviewLoad"
      @error="onWebviewError"
      :class="{ 'webview-hidden': isLoading }"
      @message="handleMessage"
      :webview-styles="webviewStyles"
    ></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoading: true,
      webviewUrl: 'http://*************:5173/',
      webviewStyles: {
        progress: {
          color: '#FF3333'
        }
      }
    }
  },
  methods: {
    // webview 加载完成事件
    onWebviewLoad() {
      console.log('webview 加载完成');
      this.isLoading = false;
    },

    handleMessage(e) {
      console.log('=== 收到H5消息 ===')
      console.log('原始事件对象:', e)
      console.log('事件类型:', typeof e)
      console.log('事件detail:', e.detail)

      let { data } = e.detail || {}
      console.log('解析出的data:', data)
      console.log('data类型:', typeof data)
      console.log('data是否为数组:', Array.isArray(data))

      if (!data) {
        console.log('❌ data为空，退出处理')
        return
      }

      // 如果data是数组，取第一个元素
      if (Array.isArray(data) && data.length > 0) {
        console.log('data是数组，取第一个元素')
        data = data[0]
        console.log('取出的实际数据:', data)
      }

      console.log('data.action:', data.action)

      // 处理下载请求
      if (data.action === 'download') {
        console.log('✅ 识别到下载请求，开始处理')
        this.handleDownload(data)
      } else {
        console.log('❌ 不是下载请求，action:', data.action)
      }
    },

    // 处理文件下载
    handleDownload(downloadData) {
      console.log('=== 开始处理下载 ===')
      console.log('下载数据:', downloadData)

      const { fileData, filename } = downloadData
      console.log('文件数据长度:', fileData ? fileData.length : 0)
      console.log('文件名:', filename)

      if (!fileData || !filename) {
        console.log('❌ 下载数据不完整')
        uni.showToast({
          title: '下载数据不完整',
          icon: 'none'
        })
        return
      }

      try {
        console.log('开始处理base64文件数据...')

        // 使用uni官方API转换base64为ArrayBuffer
        const arrayBuffer = uni.base64ToArrayBuffer(fileData)
        console.log('uni.base64ToArrayBuffer转换成功，大小:', arrayBuffer.byteLength)

        const bytes = new Uint8Array(arrayBuffer)
        console.log('Uint8Array创建成功，长度:', bytes.length)

        // 手动解码base64方案：回到指定目录
        console.log('使用手动解码base64方案')

        if (typeof plus !== 'undefined') {
          console.log('✅ plus环境可用')

          // 申请存储权限
          plus.android.requestPermissions([
            'android.permission.WRITE_EXTERNAL_STORAGE',
            'android.permission.READ_EXTERNAL_STORAGE'
          ], (success) => {
            console.log('✅ 权限申请成功')

            // 创建自定义文件夹
            const customFolderName = '/Downloads'
            const path = '/storage/emulated/0' + customFolderName
            console.log('目标路径:', path)

            // 使用Java API创建文件夹
            const File = plus.android.importClass('java.io.File')
            let dir = new File(path)

            // 文件夹不存在则创建
            if (!dir.exists()) {
              console.log('创建文件夹:', path)
              dir.mkdirs()
            }

            // 创建完整文件路径
            const fullFilePath = path + '/' + filename
            console.log('完整文件路径:', fullFilePath)

            try {
              console.log('开始手动解码base64数据...')

              // 手动解码base64
              const binaryString = atob(fileData)
              console.log('base64解码成功，长度:', binaryString.length)

              // 创建文件
              let file = new File(fullFilePath)
              console.log('File对象创建成功')

              // 使用FileOutputStream写入
              const FileOutputStream = plus.android.importClass('java.io.FileOutputStream')
              let fos = new FileOutputStream(file)
              console.log('FileOutputStream创建成功')

              // 逐字节写入
              console.log('开始逐字节写入...')
              for (let i = 0; i < binaryString.length; i++) {
                const byteValue = binaryString.charCodeAt(i) & 0xFF
                fos.write(byteValue)

                // 每1000字节输出一次进度
                if (i % 1000 === 0) {
                  console.log(`写入进度: ${i}/${binaryString.length}`)
                }
              }

              fos.close()
              console.log('文件写入完成')

              // 验证文件
              if (file.exists()) {
                const fileSize = file.length()
                console.log('✅ 文件创建成功，大小:', fileSize, 'bytes')

                if (fileSize > 0) {
                  uni.showToast({
                    title: `下载成功，已保存到${customFolderName}/${filename}`,
                    icon: 'success',
                    duration: 3000
                  })
                } else {
                  console.error('❌ 文件大小为0')
                  uni.showToast({
                    title: '文件大小为0',
                    icon: 'none'
                  })
                }
              } else {
                console.error('❌ 文件不存在')
                uni.showToast({
                  title: '文件创建失败',
                  icon: 'none'
                })
              }

            } catch (error) {
              console.error('❌ 手动解码写入失败:', error)
              console.error('错误详情:', error.message)
              uni.showToast({
                title: '写入失败',
                icon: 'none'
              })
            }

          }, (error) => {
            console.error('❌ 权限申请失败:', error)
            uni.showToast({
              title: '需要存储权限才能下载文件',
              icon: 'none'
            })
          })

        } else {
          console.log('❌ plus环境不可用')
          uni.showToast({
            title: '当前环境不支持文件下载',
            icon: 'none'
          })
        }

      } catch (error) {
        console.error('❌ 下载处理失败:', error)
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    },

    // webview 加载错误事件
    onWebviewError(error) {
      console.error('webview 加载错误:', error);
      this.isLoading = false;
      // 可以在这里添加错误处理逻辑，比如显示错误提示
    }
  }
}
</script>

<style>
.container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.webview-hidden {
  opacity: 0;
  position: absolute;
  top: -9999px;
  left: -9999px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
